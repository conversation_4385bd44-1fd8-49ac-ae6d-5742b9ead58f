import fs from "fs";
import OpenA<PERSON> from "openai";
import dotenv from "dotenv";
import { DOMParser, XMLSerializer } from "@xmldom/xmldom";

dotenv.config();

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

async function translateText(text, targetLanguage) {
  const response = await openai.chat.completions.create({
    model: "gpt-4o-mini", // cheaper & good for translation
    messages: [
      {
        role: "system",
        content: `You are a translator. Translate text into ${targetLanguage}. 
        Keep placeholders like %s, %d unchanged. Do not translate XML labels.`,
      },
      { role: "user", content: text },
    ],
  });

  return response.choices[0].message.content.trim();
}

async function main() {
  const xml = fs.readFileSync("en.xml", "utf8");

  const doc = new DOMParser().parseFromString(xml, "application/xml");
  const strings = doc.getElementsByTagName("string");

  for (let i = 0; i < strings.length; i++) {
    const node = strings[i];
    const originalText = node.textContent;

    try {
      const translated = await translateText(originalText, "Burmese"); // change to Khmer / Bengali
      node.textContent = translated;
    } catch (err) {
      console.error("Translation error:", err);
    }
  }

  const translatedXml = new XMLSerializer().serializeToString(doc);
  fs.writeFileSync("my_translated.xml", translatedXml, "utf8");
}

main();
