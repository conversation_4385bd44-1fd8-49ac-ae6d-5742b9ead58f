{"version": 3, "file": "conversations.d.ts", "sourceRoot": "", "sources": ["../../src/resources/conversations/conversations.ts"], "names": [], "mappings": "OAEO,EAAE,WAAW,EAAE;OACf,KAAK,MAAM;OACX,KAAK,QAAQ;OACb,EACL,gBAAgB,EAChB,oBAAoB,EACpB,qBAAqB,EACrB,gBAAgB,EAChB,gBAAgB,EAChB,cAAc,EACd,kBAAkB,EAClB,KAAK,EACN;OACM,KAAK,YAAY;OACjB,EAAE,UAAU,EAAE;OACd,EAAE,cAAc,EAAE;AAGzB,qBAAa,aAAc,SAAQ,WAAW;IAC5C,KAAK,EAAE,QAAQ,CAAC,KAAK,CAAoC;IAEzD;;OAEG;IACH,MAAM,CAAC,IAAI,EAAE,wBAAwB,EAAE,OAAO,CAAC,EAAE,cAAc,GAAG,UAAU,CAAC,YAAY,CAAC;IAI1F;;OAEG;IACH,QAAQ,CAAC,cAAc,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,cAAc,GAAG,UAAU,CAAC,YAAY,CAAC;IAIpF;;OAEG;IACH,MAAM,CACJ,cAAc,EAAE,MAAM,EACtB,IAAI,EAAE,wBAAwB,EAC9B,OAAO,CAAC,EAAE,cAAc,GACvB,UAAU,CAAC,YAAY,CAAC;IAI3B;;OAEG;IACH,MAAM,CAAC,cAAc,EAAE,MAAM,EAAE,OAAO,CAAC,EAAE,cAAc,GAAG,UAAU,CAAC,2BAA2B,CAAC;CAGlG;AAED,MAAM,WAAW,yBAAyB;IACxC;;OAEG;IACH,OAAO,EAAE,MAAM,GAAG,IAAI,CAAC;IAEvB;;OAEG;IACH,SAAS,EAAE,MAAM,GAAG,IAAI,CAAC;IAEzB;;;OAGG;IACH,IAAI,EAAE,qBAAqB,CAAC;CAC7B;AAED,MAAM,WAAW,yBAAyB;IACxC;;OAEG;IACH,YAAY,EAAE,MAAM,CAAC;IAErB;;OAEG;IACH,SAAS,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;IAEhB;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;IAEjB;;OAEG;IACH,WAAW,EAAE,MAAM,CAAC;IAEpB;;OAEG;IACH,IAAI,EAAE,yBAAyB,CAAC;CACjC;AAED,MAAM,WAAW,YAAY;IAC3B;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;;OAGG;IACH,UAAU,EAAE,MAAM,CAAC;IAEnB;;;;;;OAMG;IACH,QAAQ,EAAE,OAAO,CAAC;IAElB;;OAEG;IACH,MAAM,EAAE,cAAc,CAAC;CACxB;AAED,MAAM,WAAW,mBAAmB;IAClC,EAAE,EAAE,MAAM,CAAC;IAEX,OAAO,EAAE,OAAO,CAAC;IAEjB,MAAM,EAAE,sBAAsB,CAAC;CAChC;AAED,MAAM,WAAW,2BAA2B;IAC1C,EAAE,EAAE,MAAM,CAAC;IAEX,OAAO,EAAE,OAAO,CAAC;IAEjB,MAAM,EAAE,sBAAsB,CAAC;CAChC;AAED,MAAM,WAAW,gBAAgB;IAC/B;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;IAEhB;;OAEG;IACH,QAAQ,EAAE,MAAM,CAAC;IAEjB;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,IAAI,EAAE,eAAe,CAAC;CACvB;AAED,MAAM,WAAW,gBAAgB;IAC/B;;OAEG;IACH,OAAO,EAAE,MAAM,GAAG,IAAI,CAAC;IAEvB;;OAEG;IACH,IAAI,EAAE,YAAY,CAAC;IAEnB;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC;CACnB;AAED,MAAM,WAAW,iBAAiB;IAChC;;;OAGG;IACH,MAAM,EAAE,KAAK,GAAG,MAAM,GAAG,MAAM,CAAC;IAEhC;;OAEG;IACH,OAAO,EAAE,MAAM,GAAG,IAAI,CAAC;IAEvB;;;OAGG;IACH,SAAS,EAAE,MAAM,GAAG,IAAI,CAAC;IAEzB;;OAEG;IACH,IAAI,EAAE,aAAa,CAAC;CACrB;AAED,MAAM,WAAW,gBAAgB;IAC/B;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,IAAI,EAAE,YAAY,CAAC;CACpB;AAED,MAAM,WAAW,OAAO;IACtB,KAAK,EAAE,MAAM,CAAC;IAEd,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAErB,OAAO,EAAE,MAAM,CAAC;IAEhB,YAAY,EAAE,KAAK,CAAC,UAAU,CAAC,CAAC;CACjC;AAED,MAAM,WAAW,OAAO;IACtB;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;OAEG;IACH,OAAO,EAAE,KAAK,CACV,gBAAgB,GAChB,iBAAiB,GACjB,WAAW,GACX,kBAAkB,GAClB,cAAc,GACd,iBAAiB,GACjB,yBAAyB,GACzB,gBAAgB,CACnB,CAAC;IAEF;;;OAGG;IACH,IAAI,EAAE,SAAS,GAAG,MAAM,GAAG,WAAW,GAAG,QAAQ,GAAG,QAAQ,GAAG,eAAe,GAAG,WAAW,GAAG,MAAM,CAAC;IAEtG;;;OAGG;IACH,MAAM,EAAE,aAAa,GAAG,WAAW,GAAG,YAAY,CAAC;IAEnD;;OAEG;IACH,IAAI,EAAE,SAAS,CAAC;CACjB;AAED,MAAM,WAAW,iBAAiB;IAChC;;OAEG;IACH,WAAW,EAAE,KAAK,CAAC,gBAAgB,GAAG,eAAe,GAAG,yBAAyB,CAAC,CAAC;IAEnF;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,IAAI,EAAE,aAAa,CAAC;IAEpB,QAAQ,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;CAC3B;AAED,MAAM,WAAW,cAAc;IAC7B;;OAEG;IACH,OAAO,EAAE,MAAM,CAAC;IAEhB;;OAEG;IACH,IAAI,EAAE,SAAS,CAAC;CACjB;AAED,MAAM,WAAW,kBAAkB;IACjC,IAAI,EAAE,MAAM,CAAC;IAEb,IAAI,EAAE,cAAc,CAAC;CACtB;AAED,MAAM,WAAW,WAAW;IAC1B,IAAI,EAAE,MAAM,CAAC;IAEb,IAAI,EAAE,MAAM,CAAC;CACd;AAED,MAAM,WAAW,UAAU;IACzB,KAAK,EAAE,MAAM,CAAC;IAEd,KAAK,EAAE,KAAK,CAAC,MAAM,CAAC,CAAC;IAErB,OAAO,EAAE,MAAM,CAAC;CACjB;AAED,MAAM,WAAW,eAAe;IAC9B;;OAEG;IACH,SAAS,EAAE,MAAM,CAAC;IAElB;;OAEG;IACH,WAAW,EAAE,MAAM,CAAC;IAEpB;;OAEG;IACH,KAAK,EAAE,MAAM,CAAC;IAEd;;OAEG;IACH,IAAI,EAAE,cAAc,CAAC;IAErB;;OAEG;IACH,GAAG,EAAE,MAAM,CAAC;CACb;AAED,MAAM,WAAW,wBAAwB;IACvC;;;OAGG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,YAAY,CAAC,iBAAiB,CAAC,GAAG,IAAI,CAAC;IAErD;;;OAGG;IACH,QAAQ,CAAC,EAAE,MAAM,CAAC,QAAQ,GAAG,IAAI,CAAC;CACnC;AAED,MAAM,WAAW,wBAAwB;IACvC;;;;;;OAMG;IACH,QAAQ,EAAE;QAAE,CAAC,GAAG,EAAE,MAAM,GAAG,MAAM,CAAA;KAAE,CAAC;CACrC;AAID,MAAM,CAAC,OAAO,WAAW,aAAa,CAAC;IACrC,OAAO,EACL,KAAK,yBAAyB,IAAI,yBAAyB,EAC3D,KAAK,yBAAyB,IAAI,yBAAyB,EAC3D,KAAK,YAAY,IAAI,YAAY,EACjC,KAAK,mBAAmB,IAAI,mBAAmB,EAC/C,KAAK,2BAA2B,IAAI,2BAA2B,EAC/D,KAAK,gBAAgB,IAAI,gBAAgB,EACzC,KAAK,gBAAgB,IAAI,gBAAgB,EACzC,KAAK,iBAAiB,IAAI,iBAAiB,EAC3C,KAAK,gBAAgB,IAAI,gBAAgB,EACzC,KAAK,OAAO,IAAI,OAAO,EACvB,KAAK,OAAO,IAAI,OAAO,EACvB,KAAK,iBAAiB,IAAI,iBAAiB,EAC3C,KAAK,cAAc,IAAI,cAAc,EACrC,KAAK,kBAAkB,IAAI,kBAAkB,EAC7C,KAAK,WAAW,IAAI,WAAW,EAC/B,KAAK,UAAU,IAAI,UAAU,EAC7B,KAAK,eAAe,IAAI,eAAe,EACvC,KAAK,wBAAwB,IAAI,wBAAwB,EACzD,KAAK,wBAAwB,IAAI,wBAAwB,GAC1D,CAAC;IAEF,OAAO,EACL,KAAK,IAAI,KAAK,EACd,KAAK,gBAAgB,IAAI,gBAAgB,EACzC,KAAK,oBAAoB,IAAI,oBAAoB,EACjD,KAAK,qBAAqB,IAAI,qBAAqB,EACnD,KAAK,gBAAgB,IAAI,gBAAgB,EACzC,KAAK,kBAAkB,IAAI,kBAAkB,EAC7C,KAAK,cAAc,IAAI,cAAc,EACrC,KAAK,gBAAgB,IAAI,gBAAgB,GAC1C,CAAC;CACH"}