import { APIResource } from "../../core/resource.js";
import * as ConversationsAPI from "./conversations.js";
import * as ResponsesAPI from "../responses/responses.js";
import { APIPromise } from "../../core/api-promise.js";
import { ConversationCursorPage, type ConversationCursorPageParams, PagePromise } from "../../core/pagination.js";
import { RequestOptions } from "../../internal/request-options.js";
export declare class Items extends APIResource {
    /**
     * Create items in a conversation with the given ID.
     */
    create(conversationID: string, params: ItemCreateParams, options?: RequestOptions): APIPromise<ConversationItemList>;
    /**
     * Get a single item from a conversation with the given IDs.
     */
    retrieve(itemID: string, params: ItemRetrieveParams, options?: RequestOptions): APIPromise<ConversationItem>;
    /**
     * List all items for a conversation with the given ID.
     */
    list(conversationID: string, query?: ItemListParams | null | undefined, options?: RequestOptions): PagePromise<ConversationItemsPage, ConversationItem>;
    /**
     * Delete an item from a conversation with the given IDs.
     */
    delete(itemID: string, params: ItemDeleteParams, options?: RequestOptions): APIPromise<ConversationsAPI.Conversation>;
}
export type ConversationItemsPage = ConversationCursorPage<ConversationItem>;
/**
 * A single item within a conversation. The set of possible types are the same as
 * the `output` type of a
 * [Response object](https://platform.openai.com/docs/api-reference/responses/object#responses/object-output).
 */
export type ConversationItem = ConversationsAPI.Message | ResponsesAPI.ResponseFunctionToolCallItem | ResponsesAPI.ResponseFunctionToolCallOutputItem | ResponsesAPI.ResponseFileSearchToolCall | ResponsesAPI.ResponseFunctionWebSearch | ConversationItem.ImageGenerationCall | ResponsesAPI.ResponseComputerToolCall | ResponsesAPI.ResponseComputerToolCallOutputItem | ResponsesAPI.ResponseReasoningItem | ResponsesAPI.ResponseCodeInterpreterToolCall | ConversationItem.LocalShellCall | ConversationItem.LocalShellCallOutput | ConversationItem.McpListTools | ConversationItem.McpApprovalRequest | ConversationItem.McpApprovalResponse | ConversationItem.McpCall | ResponsesAPI.ResponseCustomToolCall | ResponsesAPI.ResponseCustomToolCallOutput;
export declare namespace ConversationItem {
    /**
     * An image generation request made by the model.
     */
    interface ImageGenerationCall {
        /**
         * The unique ID of the image generation call.
         */
        id: string;
        /**
         * The generated image encoded in base64.
         */
        result: string | null;
        /**
         * The status of the image generation call.
         */
        status: 'in_progress' | 'completed' | 'generating' | 'failed';
        /**
         * The type of the image generation call. Always `image_generation_call`.
         */
        type: 'image_generation_call';
    }
    /**
     * A tool call to run a command on the local shell.
     */
    interface LocalShellCall {
        /**
         * The unique ID of the local shell call.
         */
        id: string;
        /**
         * Execute a shell command on the server.
         */
        action: LocalShellCall.Action;
        /**
         * The unique ID of the local shell tool call generated by the model.
         */
        call_id: string;
        /**
         * The status of the local shell call.
         */
        status: 'in_progress' | 'completed' | 'incomplete';
        /**
         * The type of the local shell call. Always `local_shell_call`.
         */
        type: 'local_shell_call';
    }
    namespace LocalShellCall {
        /**
         * Execute a shell command on the server.
         */
        interface Action {
            /**
             * The command to run.
             */
            command: Array<string>;
            /**
             * Environment variables to set for the command.
             */
            env: {
                [key: string]: string;
            };
            /**
             * The type of the local shell action. Always `exec`.
             */
            type: 'exec';
            /**
             * Optional timeout in milliseconds for the command.
             */
            timeout_ms?: number | null;
            /**
             * Optional user to run the command as.
             */
            user?: string | null;
            /**
             * Optional working directory to run the command in.
             */
            working_directory?: string | null;
        }
    }
    /**
     * The output of a local shell tool call.
     */
    interface LocalShellCallOutput {
        /**
         * The unique ID of the local shell tool call generated by the model.
         */
        id: string;
        /**
         * A JSON string of the output of the local shell tool call.
         */
        output: string;
        /**
         * The type of the local shell tool call output. Always `local_shell_call_output`.
         */
        type: 'local_shell_call_output';
        /**
         * The status of the item. One of `in_progress`, `completed`, or `incomplete`.
         */
        status?: 'in_progress' | 'completed' | 'incomplete' | null;
    }
    /**
     * A list of tools available on an MCP server.
     */
    interface McpListTools {
        /**
         * The unique ID of the list.
         */
        id: string;
        /**
         * The label of the MCP server.
         */
        server_label: string;
        /**
         * The tools available on the server.
         */
        tools: Array<McpListTools.Tool>;
        /**
         * The type of the item. Always `mcp_list_tools`.
         */
        type: 'mcp_list_tools';
        /**
         * Error message if the server could not list tools.
         */
        error?: string | null;
    }
    namespace McpListTools {
        /**
         * A tool available on an MCP server.
         */
        interface Tool {
            /**
             * The JSON schema describing the tool's input.
             */
            input_schema: unknown;
            /**
             * The name of the tool.
             */
            name: string;
            /**
             * Additional annotations about the tool.
             */
            annotations?: unknown | null;
            /**
             * The description of the tool.
             */
            description?: string | null;
        }
    }
    /**
     * A request for human approval of a tool invocation.
     */
    interface McpApprovalRequest {
        /**
         * The unique ID of the approval request.
         */
        id: string;
        /**
         * A JSON string of arguments for the tool.
         */
        arguments: string;
        /**
         * The name of the tool to run.
         */
        name: string;
        /**
         * The label of the MCP server making the request.
         */
        server_label: string;
        /**
         * The type of the item. Always `mcp_approval_request`.
         */
        type: 'mcp_approval_request';
    }
    /**
     * A response to an MCP approval request.
     */
    interface McpApprovalResponse {
        /**
         * The unique ID of the approval response
         */
        id: string;
        /**
         * The ID of the approval request being answered.
         */
        approval_request_id: string;
        /**
         * Whether the request was approved.
         */
        approve: boolean;
        /**
         * The type of the item. Always `mcp_approval_response`.
         */
        type: 'mcp_approval_response';
        /**
         * Optional reason for the decision.
         */
        reason?: string | null;
    }
    /**
     * An invocation of a tool on an MCP server.
     */
    interface McpCall {
        /**
         * The unique ID of the tool call.
         */
        id: string;
        /**
         * A JSON string of the arguments passed to the tool.
         */
        arguments: string;
        /**
         * The name of the tool that was run.
         */
        name: string;
        /**
         * The label of the MCP server running the tool.
         */
        server_label: string;
        /**
         * The type of the item. Always `mcp_call`.
         */
        type: 'mcp_call';
        /**
         * The error from the tool call, if any.
         */
        error?: string | null;
        /**
         * The output from the tool call.
         */
        output?: string | null;
    }
}
/**
 * A list of Conversation items.
 */
export interface ConversationItemList {
    /**
     * A list of conversation items.
     */
    data: Array<ConversationItem>;
    /**
     * The ID of the first item in the list.
     */
    first_id: string;
    /**
     * Whether there are more items available.
     */
    has_more: boolean;
    /**
     * The ID of the last item in the list.
     */
    last_id: string;
    /**
     * The type of object returned, must be `list`.
     */
    object: 'list';
}
export interface ItemCreateParams {
    /**
     * Body param: The items to add to the conversation. You may add up to 20 items at
     * a time.
     */
    items: Array<ResponsesAPI.ResponseInputItem>;
    /**
     * Query param: Additional fields to include in the response. See the `include`
     * parameter for
     * [listing Conversation items above](https://platform.openai.com/docs/api-reference/conversations/list-items#conversations_list_items-include)
     * for more information.
     */
    include?: Array<ResponsesAPI.ResponseIncludable>;
}
export interface ItemRetrieveParams {
    /**
     * Path param: The ID of the conversation that contains the item.
     */
    conversation_id: string;
    /**
     * Query param: Additional fields to include in the response. See the `include`
     * parameter for
     * [listing Conversation items above](https://platform.openai.com/docs/api-reference/conversations/list-items#conversations_list_items-include)
     * for more information.
     */
    include?: Array<ResponsesAPI.ResponseIncludable>;
}
export interface ItemListParams extends ConversationCursorPageParams {
    /**
     * Specify additional output data to include in the model response. Currently
     * supported values are:
     *
     * - `web_search_call.action.sources`: Include the sources of the web search tool
     *   call.
     * - `code_interpreter_call.outputs`: Includes the outputs of python code execution
     *   in code interpreter tool call items.
     * - `computer_call_output.output.image_url`: Include image urls from the computer
     *   call output.
     * - `file_search_call.results`: Include the search results of the file search tool
     *   call.
     * - `message.input_image.image_url`: Include image urls from the input message.
     * - `message.output_text.logprobs`: Include logprobs with assistant messages.
     * - `reasoning.encrypted_content`: Includes an encrypted version of reasoning
     *   tokens in reasoning item outputs. This enables reasoning items to be used in
     *   multi-turn conversations when using the Responses API statelessly (like when
     *   the `store` parameter is set to `false`, or when an organization is enrolled
     *   in the zero data retention program).
     */
    include?: Array<ResponsesAPI.ResponseIncludable>;
    /**
     * The order to return the input items in. Default is `desc`.
     *
     * - `asc`: Return the input items in ascending order.
     * - `desc`: Return the input items in descending order.
     */
    order?: 'asc' | 'desc';
}
export interface ItemDeleteParams {
    /**
     * The ID of the conversation that contains the item.
     */
    conversation_id: string;
}
export declare namespace Items {
    export { type ConversationItem as ConversationItem, type ConversationItemList as ConversationItemList, type ConversationItemsPage as ConversationItemsPage, type ItemCreateParams as ItemCreateParams, type ItemRetrieveParams as ItemRetrieveParams, type ItemListParams as ItemListParams, type ItemDeleteParams as ItemDeleteParams, };
}
//# sourceMappingURL=items.d.ts.map