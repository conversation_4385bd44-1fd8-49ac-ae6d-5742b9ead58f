"use strict";
// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClientSecrets = void 0;
const resource_1 = require("../../core/resource.js");
class ClientSecrets extends resource_1.APIResource {
    /**
     * Create a Realtime client secret with an associated session configuration.
     */
    create(body, options) {
        return this._client.post('/realtime/client_secrets', { body, ...options });
    }
}
exports.ClientSecrets = ClientSecrets;
//# sourceMappingURL=client-secrets.js.map